import * as React from "react";
import { ChevronLeft, ChevronRight } from "lucide-react";
import { cn } from "@/lib/utils";

// Types and interfaces
export interface CalendarDate {
  date: Date;
  isCurrentMonth: boolean;
  isToday: boolean;
  isSelected: boolean;
  isDisabled: boolean;
  isWeekend: boolean;
}

export interface CalendarProps {
  mode?: "single" | "multiple" | "range";
  selected?: Date | Date[] | { from?: Date; to?: Date };
  onSelect?: (
    date: Date | Date[] | { from?: Date; to?: Date } | undefined
  ) => void;
  disabled?: (date: Date) => boolean | Date[] | boolean;
  className?: string;
  initialFocus?: boolean;
  showOutsideDays?: boolean;
  weekStartsOn?: 0 | 1 | 2 | 3 | 4 | 5 | 6;
  locale?: string;
  dir?: "ltr" | "rtl";
}

// Utility functions
const DAYS_IN_WEEK = 7;
const WEEKS_IN_CALENDAR = 6;

const getMonthNames = (locale: string = "en-US"): string[] => {
  const months = [];
  for (let i = 0; i < 12; i++) {
    const date = new Date(2024, i, 1);
    months.push(date.toLocaleDateString(locale, { month: "long" }));
  }
  return months;
};

const getWeekdayNames = (
  locale: string = "en-US",
  weekStartsOn: number = 0
): string[] => {
  const weekdays = [];
  for (let i = 0; i < 7; i++) {
    const date = new Date(2024, 0, ((weekStartsOn + i) % 7) + 1);
    weekdays.push(date.toLocaleDateString(locale, { weekday: "short" }));
  }
  return weekdays;
};

const isSameDay = (date1: Date, date2: Date): boolean => {
  return (
    date1.getFullYear() === date2.getFullYear() &&
    date1.getMonth() === date2.getMonth() &&
    date1.getDate() === date2.getDate()
  );
};

const isWeekend = (date: Date): boolean => {
  const day = date.getDay();
  return day === 0 || day === 6; // Sunday or Saturday
};

const generateCalendarDates = (
  year: number,
  month: number,
  weekStartsOn: number = 0,
  showOutsideDays: boolean = true
): CalendarDate[][] => {
  const firstDayOfMonth = new Date(year, month, 1);
  const lastDayOfMonth = new Date(year, month + 1, 0);
  const firstDayOfWeek = (firstDayOfMonth.getDay() - weekStartsOn + 7) % 7;

  const weeks: CalendarDate[][] = [];
  const today = new Date();
  today.setHours(0, 0, 0, 0);

  for (let week = 0; week < WEEKS_IN_CALENDAR; week++) {
    const days: CalendarDate[] = [];

    for (let day = 0; day < DAYS_IN_WEEK; day++) {
      const dayIndex = week * DAYS_IN_WEEK + day;
      const dateOffset = dayIndex - firstDayOfWeek;
      const currentDate = new Date(year, month, 1 + dateOffset);

      const isCurrentMonth = currentDate.getMonth() === month;
      const shouldShow = showOutsideDays || isCurrentMonth;

      if (shouldShow) {
        days.push({
          date: currentDate,
          isCurrentMonth,
          isToday: isSameDay(currentDate, today),
          isSelected: false, // Will be updated by parent component
          isDisabled: false, // Will be updated by parent component
          isWeekend: isWeekend(currentDate),
        });
      } else {
        days.push({
          date: currentDate,
          isCurrentMonth: false,
          isToday: false,
          isSelected: false,
          isDisabled: true,
          isWeekend: isWeekend(currentDate),
        });
      }
    }

    weeks.push(days);
  }

  return weeks;
};

// Calendar component
export function Calendar({
  mode = "single",
  selected,
  onSelect,
  disabled,
  className,
  initialFocus = false,
  showOutsideDays = true,
  weekStartsOn = 0,
  locale = "en-US",
  dir = "ltr",
  ...props
}: CalendarProps) {
  const [currentDate, setCurrentDate] = React.useState(new Date());
  const [focusedDate, setFocusedDate] = React.useState<Date | null>(null);
  const calendarRef = React.useRef<HTMLDivElement>(null);

  const currentYear = currentDate.getFullYear();
  const currentMonth = currentDate.getMonth();

  const monthNames = React.useMemo(() => getMonthNames(locale), [locale]);
  const weekdayNames = React.useMemo(
    () => getWeekdayNames(locale, weekStartsOn),
    [locale, weekStartsOn]
  );

  const calendarWeeks = React.useMemo(
    () =>
      generateCalendarDates(
        currentYear,
        currentMonth,
        weekStartsOn,
        showOutsideDays
      ),
    [currentYear, currentMonth, weekStartsOn, showOutsideDays]
  );

  // Check if a date is disabled
  const isDateDisabled = React.useCallback(
    (date: Date): boolean => {
      if (!disabled) return false;

      if (typeof disabled === "function") {
        return disabled(date);
      }

      if (Array.isArray(disabled)) {
        return disabled.some((disabledDate) => isSameDay(date, disabledDate));
      }

      return Boolean(disabled);
    },
    [disabled]
  );

  // Check if a date is selected
  const isDateSelected = React.useCallback(
    (date: Date): boolean => {
      if (!selected) return false;

      if (mode === "single") {
        return selected instanceof Date && isSameDay(date, selected);
      }

      if (mode === "multiple") {
        return (
          Array.isArray(selected) &&
          selected.some((selectedDate) => isSameDay(date, selectedDate))
        );
      }

      if (mode === "range") {
        const range = selected as { from?: Date; to?: Date };
        if (!range.from) return false;
        if (!range.to) return isSameDay(date, range.from);
        return date >= range.from && date <= range.to;
      }

      return false;
    },
    [selected, mode]
  );

  // Navigation functions
  const navigateMonth = (direction: "prev" | "next") => {
    setCurrentDate((prev) => {
      const newDate = new Date(prev);
      if (direction === "prev") {
        newDate.setMonth(newDate.getMonth() - 1);
      } else {
        newDate.setMonth(newDate.getMonth() + 1);
      }
      return newDate;
    });
  };

  const navigateYear = (direction: "prev" | "next") => {
    setCurrentDate((prev) => {
      const newDate = new Date(prev);
      if (direction === "prev") {
        newDate.setFullYear(newDate.getFullYear() - 1);
      } else {
        newDate.setFullYear(newDate.getFullYear() + 1);
      }
      return newDate;
    });
  };

  // Date selection handler
  const handleDateSelect = (date: Date) => {
    if (isDateDisabled(date)) return;

    if (mode === "single") {
      onSelect?.(date);
    } else if (mode === "multiple") {
      const currentSelected = (selected as Date[]) || [];
      const isAlreadySelected = currentSelected.some((selectedDate) =>
        isSameDay(date, selectedDate)
      );

      if (isAlreadySelected) {
        onSelect?.(
          currentSelected.filter(
            (selectedDate) => !isSameDay(date, selectedDate)
          )
        );
      } else {
        onSelect?.([...currentSelected, date]);
      }
    } else if (mode === "range") {
      const range = (selected as { from?: Date; to?: Date }) || {};

      if (!range.from || (range.from && range.to)) {
        onSelect?.({ from: date, to: undefined });
      } else if (range.from && !range.to) {
        if (date < range.from) {
          onSelect?.({ from: date, to: range.from });
        } else {
          onSelect?.({ from: range.from, to: date });
        }
      }
    }
  };

  // Keyboard navigation handler
  const handleKeyDown = (event: React.KeyboardEvent, date: Date) => {
    const { key } = event;
    let newFocusDate: Date | null = null;

    switch (key) {
      case "ArrowLeft":
        newFocusDate = new Date(date);
        newFocusDate.setDate(date.getDate() - 1);
        break;
      case "ArrowRight":
        newFocusDate = new Date(date);
        newFocusDate.setDate(date.getDate() + 1);
        break;
      case "ArrowUp":
        newFocusDate = new Date(date);
        newFocusDate.setDate(date.getDate() - 7);
        break;
      case "ArrowDown":
        newFocusDate = new Date(date);
        newFocusDate.setDate(date.getDate() + 7);
        break;
      case "Home":
        newFocusDate = new Date(date.getFullYear(), date.getMonth(), 1);
        break;
      case "End":
        newFocusDate = new Date(date.getFullYear(), date.getMonth() + 1, 0);
        break;
      case "PageUp":
        newFocusDate = new Date(date);
        newFocusDate.setMonth(date.getMonth() - 1);
        break;
      case "PageDown":
        newFocusDate = new Date(date);
        newFocusDate.setMonth(date.getMonth() + 1);
        break;
      case "Enter":
      case " ":
        event.preventDefault();
        handleDateSelect(date);
        return;
      default:
        return;
    }

    if (newFocusDate) {
      event.preventDefault();
      setFocusedDate(newFocusDate);

      // Update current month if needed
      if (
        newFocusDate.getMonth() !== currentMonth ||
        newFocusDate.getFullYear() !== currentYear
      ) {
        setCurrentDate(newFocusDate);
      }
    }
  };

  // Focus management for accessibility
  React.useEffect(() => {
    if (initialFocus && calendarRef.current) {
      const firstFocusableDate = calendarRef.current.querySelector(
        '[role="gridcell"]:not([aria-disabled="true"])'
      ) as HTMLElement;
      firstFocusableDate?.focus();
    }
  }, [initialFocus]);

  React.useEffect(() => {
    if (focusedDate && calendarRef.current) {
      const focusableElement = calendarRef.current.querySelector(
        `[data-date="${focusedDate.toISOString().split("T")[0]}"]`
      ) as HTMLElement;
      focusableElement?.focus();
    }
  }, [focusedDate]);

  return (
    <div
      ref={calendarRef}
      className={cn(
        "calendar-container bg-white rounded-xl shadow-lg border border-gray-200 p-4",
        "focus-within:ring-2 focus-within:ring-blue-500 focus-within:ring-offset-2",
        className
      )}
      dir={dir}
      role="application"
      aria-label="Calendar"
      {...props}
    >
      {/* Calendar Header */}
      <div className="calendar-header flex items-center justify-between mb-4">
        <div className="flex items-center space-x-1">
          <button
            type="button"
            onClick={() => navigateYear("prev")}
            className="nav-button p-1 rounded-md hover:bg-gray-100 focus:outline-none focus:ring-2 focus:ring-blue-500"
            aria-label="Previous year"
          >
            <ChevronLeft className="h-4 w-4" />
            <ChevronLeft className="h-4 w-4 -ml-2" />
          </button>
          <button
            type="button"
            onClick={() => navigateMonth("prev")}
            className="nav-button p-1 rounded-md hover:bg-gray-100 focus:outline-none focus:ring-2 focus:ring-blue-500"
            aria-label="Previous month"
          >
            <ChevronLeft className="h-4 w-4" />
          </button>
        </div>

        <h2 className="text-lg font-semibold text-gray-900">
          {monthNames[currentMonth]} {currentYear}
        </h2>

        <div className="flex items-center space-x-1">
          <button
            type="button"
            onClick={() => navigateMonth("next")}
            className="nav-button p-1 rounded-md hover:bg-gray-100 focus:outline-none focus:ring-2 focus:ring-blue-500"
            aria-label="Next month"
          >
            <ChevronRight className="h-4 w-4" />
          </button>
          <button
            type="button"
            onClick={() => navigateYear("next")}
            className="nav-button p-1 rounded-md hover:bg-gray-100 focus:outline-none focus:ring-2 focus:ring-blue-500"
            aria-label="Next year"
          >
            <ChevronRight className="h-4 w-4" />
            <ChevronRight className="h-4 w-4 -ml-2" />
          </button>
        </div>
      </div>

      {/* Calendar Grid */}
      <div
        className="calendar-grid"
        role="grid"
        aria-label={`${monthNames[currentMonth]} ${currentYear}`}
      >
        {/* Weekday Headers */}
        <div className="weekdays grid grid-cols-7 gap-1 mb-2" role="row">
          {weekdayNames.map((weekday, index) => (
            <div
              key={index}
              className="weekday-header text-center text-xs font-medium text-gray-500 py-2"
              role="columnheader"
              aria-label={weekday}
            >
              {weekday}
            </div>
          ))}
        </div>

        {/* Calendar Days */}
        <div className="weeks space-y-1">
          {calendarWeeks.map((week, weekIndex) => (
            <div
              key={weekIndex}
              className="week grid grid-cols-7 gap-1"
              role="row"
            >
              {week.map((calendarDate, dayIndex) => {
                const { date, isCurrentMonth, isToday, isWeekend } =
                  calendarDate;
                const selected = isDateSelected(date);
                const disabled = isDateDisabled(date);
                const dateString = date.toISOString().split("T")[0];

                return (
                  <button
                    key={dayIndex}
                    type="button"
                    onClick={() => handleDateSelect(date)}
                    onKeyDown={(e) => handleKeyDown(e, date)}
                    disabled={disabled}
                    data-date={dateString}
                    className={cn(
                      "day-button h-10 w-10 rounded-full text-sm font-medium transition-all duration-200",
                      "focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-1",
                      "hover:bg-blue-50 hover:text-blue-900",
                      {
                        // Current month styling
                        "text-gray-900":
                          isCurrentMonth && !selected && !disabled,
                        "text-gray-400": !isCurrentMonth && !selected,

                        // Today styling
                        "bg-blue-100 text-blue-900 font-semibold ring-2 ring-blue-400":
                          isToday && !selected,

                        // Selected styling
                        "bg-blue-600 text-white hover:bg-blue-700":
                          selected && !disabled,

                        // Weekend styling
                        "text-red-600":
                          isWeekend && isCurrentMonth && !selected && !disabled,

                        // Disabled styling
                        "text-gray-300 cursor-not-allowed hover:bg-transparent":
                          disabled,

                        // Outside days
                        "opacity-50": !isCurrentMonth && showOutsideDays,
                      }
                    )}
                    role="gridcell"
                    aria-selected={selected}
                    aria-disabled={disabled}
                    aria-label={date.toLocaleDateString(locale, {
                      weekday: "long",
                      year: "numeric",
                      month: "long",
                      day: "numeric",
                    })}
                    tabIndex={selected || (isToday && !selected) ? 0 : -1}
                  >
                    {date.getDate()}
                  </button>
                );
              })}
            </div>
          ))}
        </div>
      </div>
    </div>
  );
}

Calendar.displayName = "Calendar";

export { Calendar as default };
