import { useTranslation } from "react-i18next";
import { useLanguage } from "@/contexts/LanguageContext";
import { Card, CardContent } from "@/components/ui/card";
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar";
import { Badge } from "@/components/ui/badge";
import { Star, Quote, Heart, MapPin, X } from "lucide-react";
import { useState } from "react";
import { useToast } from "@/hooks/use-toast";

const TestimonialsSection = () => {
  const { t } = useTranslation();
  const { isRTL } = useLanguage();
  const { toast } = useToast();
  const [showReviewModal, setShowReviewModal] = useState(false);
  const [selectedRating, setSelectedRating] = useState(0);
  const [reviewText, setReviewText] = useState("");
  const [isSubmitting, setIsSubmitting] = useState(false);

  const testimonials = [
    {
      id: 1,
      name: "<PERSON>",
      location: "New York, NY",
      avatar: "SC",
      rating: 5,
      textKey: "testimonials.sarah.text",
      verified: true,
      stayDuration: "3 nights",
      property: "Dubai Marina Penthouse",
    },
    {
      id: 2,
      name: "Marcus Johnson",
      location: "London, UK",
      avatar: "MJ",
      rating: 5,
      textKey: "testimonials.marcus.text",
      verified: true,
      stayDuration: "1 week",
      property: "NYC Central Park View",
    },
    {
      id: 3,
      name: "Elena Rodriguez",
      location: "Barcelona, Spain",
      avatar: "ER",
      rating: 5,
      textKey: "testimonials.elena.text",
      verified: true,
      stayDuration: "5 nights",
      property: "London Thames Apartment",
    },
  ];

  const handleSubmitReview = async (e: React.FormEvent) => {
    e.preventDefault();

    if (selectedRating === 0) {
      toast({
        title: t("testimonials.addReview.error.title", "Rating Required"),
        description: t(
          "testimonials.addReview.error.ratingRequired",
          "Please select a rating before submitting your review."
        ),
        variant: "destructive",
      });
      return;
    }

    if (!reviewText.trim()) {
      toast({
        title: t("testimonials.addReview.error.title", "Review Required"),
        description: t(
          "testimonials.addReview.error.reviewRequired",
          "Please write your review before submitting."
        ),
        variant: "destructive",
      });
      return;
    }

    setIsSubmitting(true);

    try {
      // Simulate API call - replace with actual review submission
      await new Promise((resolve) => setTimeout(resolve, 1000));

      // Show success message
      toast({
        title: t("testimonials.addReview.success.title", "Review Submitted!"),
        description: t(
          "testimonials.addReview.success.description",
          "Thank you for sharing your experience. Your review has been submitted successfully."
        ),
        variant: "success",
      });

      // Reset form and close modal
      setReviewText("");
      setSelectedRating(0);
      setShowReviewModal(false);
    } catch (error) {
      toast({
        title: t("testimonials.addReview.error.title", "Submission Failed"),
        description: t(
          "testimonials.addReview.error.submissionFailed",
          "There was an error submitting your review. Please try again."
        ),
        variant: "destructive",
      });
    } finally {
      setIsSubmitting(false);
    }
  };

  return (
    <section className="py-24 bg-gradient-warm relative overflow-hidden">
      {/* Background decorations */}
      <div className="absolute top-20 right-20 opacity-10">
        <Quote className="w-32 h-32 text-primary" />
      </div>
      <div className="absolute bottom-20 left-20 opacity-10">
        <Heart className="w-24 h-24 text-primary" />
      </div>

      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div className="text-center mb-20">
          <div className="inline-flex items-center bg-primary/10 rounded-full px-4 py-2 mb-6">
            <Heart className="w-4 h-4 text-primary mr-2" />
            <span className="text-primary text-sm font-semibold">
              Guest Stories
            </span>
          </div>
          <h2 className="text-3xl md:text-4xl lg:text-5xl font-bold text-foreground mb-6 leading-tight">
            {t("testimonials.title")}
          </h2>
          <p className="text-lg text-muted-foreground max-w-3xl mx-auto leading-relaxed">
            {t("testimonials.description")}
          </p>
        </div>

        <div className="grid grid-cols-1 md:grid-cols-3 gap-8">
          {testimonials.map((testimonial, index) => (
            <Card
              key={testimonial.id}
              className="group border-0 shadow-soft hover:shadow-elevated transition-all duration-500 cursor-pointer hover:-translate-y-2 bg-gradient-to-br from-background to-brand-warm/30"
              style={{ animationDelay: `${index * 150}ms` }}
            >
              <CardContent className="p-8 relative overflow-hidden">
                {/* Quote decoration */}
                <div className="absolute top-4 right-4 opacity-10">
                  <Quote className="w-8 h-8 text-primary" />
                </div>

                {/* Rating Stars */}
                <div className="flex items-center justify-between mb-6">
                  <div className="flex items-center space-x-1">
                    {[...Array(testimonial.rating)].map((_, i) => (
                      <Star
                        key={i}
                        className="h-5 w-5 fill-yellow-400 text-yellow-400"
                      />
                    ))}
                  </div>
                  {testimonial.verified && (
                    <Badge className="bg-green-100 text-green-700 hover:bg-green-200">
                      ✓ Verified
                    </Badge>
                  )}
                </div>

                {/* Testimonial Text */}
                <blockquote className="text-foreground leading-relaxed mb-6 relative">
                  <span className="text-primary text-4xl font-serif absolute -top-2 -left-1 opacity-20">
                    "
                  </span>
                  <span className="relative z-10">
                    {t(testimonial.textKey)}
                  </span>
                </blockquote>

                {/* Author */}
                <div
                  className={`flex items-center ${
                    isRTL ? "space-x-reverse space-x-4" : "space-x-4"
                  }`}
                >
                  <Avatar className="h-14 w-14 ring-2 ring-primary/20">
                    <AvatarImage src="" alt={testimonial.name} />
                    <AvatarFallback className="bg-gradient-ocean text-white font-bold text-lg">
                      {testimonial.avatar}
                    </AvatarFallback>
                  </Avatar>
                  <div>
                    <p className="font-bold text-foreground group-hover:text-primary transition-colors duration-300">
                      {testimonial.name}
                    </p>
                    <p className="text-sm text-muted-foreground flex items-center">
                      <MapPin className="w-3 h-3 mr-1" />
                      {testimonial.location}
                    </p>
                  </div>
                </div>

                {/* Decorative line */}
                <div className="absolute bottom-0 left-0 w-full h-1 bg-gradient-to-r from-transparent via-primary/30 to-transparent transform scale-x-0 group-hover:scale-x-100 transition-transform duration-300"></div>
              </CardContent>
            </Card>
          ))}
        </div>

        {/* Add Review Button */}
        <div className="text-center mt-16">
          <button
            onClick={() => {
              setShowReviewModal(true);
              setSelectedRating(0);
            }}
            className="bg-gradient-ocean text-white font-semibold py-4 px-8 rounded-xl hover:shadow-lg transform hover:-translate-y-1 transition-all duration-300 focus:ring-4 focus:ring-primary/20"
          >
            {t("testimonials.addReview.button", "Share Your Experience")}
          </button>
        </div>

        {/* Review Modal */}
        {showReviewModal && (
          <div
            className="fixed inset-0 bg-black/50 backdrop-blur-sm z-50 flex items-center justify-center p-4"
            onClick={() => {
              setShowReviewModal(false);
              setSelectedRating(0);
              setReviewText("");
            }}
          >
            <div
              className={`bg-background/95 backdrop-blur-sm rounded-2xl p-8 shadow-soft border border-primary/10 max-w-2xl w-full max-h-[90vh] overflow-y-auto ${
                isRTL ? "text-right" : "text-left"
              }`}
              onClick={(e) => e.stopPropagation()}
              dir={isRTL ? "rtl" : "ltr"}
            >
              <div
                className={`flex ${
                  isRTL ? "flex-row-reverse" : "flex-row"
                } justify-between items-center mb-6`}
              >
                <h3 className="text-2xl font-bold text-foreground">
                  {t("testimonials.addReview.title", "Share Your Experience")}
                </h3>
                <button
                  onClick={() => {
                    setShowReviewModal(false);
                    setSelectedRating(0);
                    setReviewText("");
                  }}
                  className="p-2 hover:bg-muted rounded-lg transition-colors duration-200"
                >
                  <X className="w-6 h-6 text-muted-foreground" />
                </button>
              </div>

              <p className="text-muted-foreground mb-6">
                {t(
                  "testimonials.addReview.description",
                  "Help other travelers by sharing your luxury accommodation experience"
                )}
              </p>

              <form className="space-y-6" onSubmit={handleSubmitReview}>
                {/* Rating */}
                <div className="text-center">
                  <label className="block text-sm font-medium text-foreground mb-3">
                    {t("testimonials.addReview.rating", "Your Rating")}
                  </label>
                  <div
                    className={`flex items-center justify-center ${
                      isRTL ? "space-x-reverse space-x-2" : "space-x-2"
                    }`}
                  >
                    {[1, 2, 3, 4, 5].map((star) => (
                      <button
                        key={star}
                        type="button"
                        className="text-2xl hover:scale-110 transition-transform duration-200"
                        onClick={() => setSelectedRating(star)}
                      >
                        <Star
                          className={`h-8 w-8 transition-colors duration-200 ${
                            star <= selectedRating
                              ? "fill-yellow-400 text-yellow-400"
                              : "fill-gray-300 text-gray-300 hover:fill-yellow-200 hover:text-yellow-200"
                          }`}
                        />
                      </button>
                    ))}
                  </div>
                </div>

                {/* Review Text */}
                <div>
                  <label
                    htmlFor="review-text"
                    className={`block text-sm font-medium text-foreground mb-2 ${
                      isRTL ? "text-right" : "text-left"
                    }`}
                  >
                    {t("testimonials.addReview.review", "Your Review")}
                  </label>
                  <textarea
                    id="review-text"
                    rows={4}
                    value={reviewText}
                    onChange={(e) => setReviewText(e.target.value)}
                    className={`w-full px-4 py-3 border border-border rounded-lg bg-background/50 backdrop-blur-sm text-foreground placeholder-muted-foreground focus:ring-2 focus:ring-primary/20 focus:border-primary transition-all duration-200 resize-none ${
                      isRTL ? "text-right" : "text-left"
                    }`}
                    placeholder={t(
                      "testimonials.addReview.placeholder",
                      "Tell us about your experience..."
                    )}
                    dir={isRTL ? "rtl" : "ltr"}
                  />
                </div>

                {/* Submit Button */}
                <div className="pt-4">
                  <button
                    type="submit"
                    disabled={isSubmitting}
                    className="w-full bg-gradient-ocean text-white font-semibold py-4 px-8 rounded-xl hover:shadow-lg transform hover:-translate-y-1 transition-all duration-300 focus:ring-4 focus:ring-primary/20 disabled:opacity-50 disabled:cursor-not-allowed disabled:transform-none"
                  >
                    {isSubmitting
                      ? t("testimonials.addReview.submitting", "Submitting...")
                      : t("testimonials.addReview.submit", "Submit Review")}
                  </button>
                </div>
              </form>
            </div>
          </div>
        )}
      </div>
    </section>
  );
};

export default TestimonialsSection;
