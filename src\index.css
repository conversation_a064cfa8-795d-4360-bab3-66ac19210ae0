@tailwind base;
@tailwind components;
@tailwind utilities;

/* Definition of the design system. All colors, gradients, fonts, etc should be defined here. 
All colors MUST be HSL.
*/

@layer base {
  :root {
    --background: 0 0% 100%;
    --foreground: 208 25% 20%;

    --card: 0 0% 100%;
    --card-foreground: 208 25% 20%;

    --popover: 0 0% 100%;
    --popover-foreground: 208 25% 20%;

    --primary: 195 85% 41%;
    --primary-foreground: 0 0% 100%;

    --secondary: 195 15% 95%;
    --secondary-foreground: 208 25% 20%;

    --muted: 195 15% 96%;
    --muted-foreground: 208 15% 50%;

    --accent: 195 55% 85%;
    --accent-foreground: 208 25% 20%;

    --destructive: 0 84.2% 60.2%;
    --destructive-foreground: 210 40% 98%;

    --border: 195 15% 90%;
    --input: 195 15% 94%;
    --ring: 195 85% 41%;

    --radius: 0.75rem;

    /* Hala Brand Colors */
    --brand-teal: 195 85% 41%;
    --brand-teal-light: 195 55% 85%;
    --brand-teal-dark: 195 85% 35%;
    --brand-ocean: 200 40% 60%;
    --brand-warm: 45 30% 92%;

    /* Gradients */
    --gradient-ocean: linear-gradient(
      135deg,
      hsl(var(--brand-teal)) 0%,
      hsl(var(--brand-ocean)) 100%
    );
    --gradient-warm: linear-gradient(
      135deg,
      hsl(var(--brand-warm)) 0%,
      hsl(var(--background)) 100%
    );

    /* Shadows */
    --shadow-soft: 0 4px 20px -4px hsl(var(--brand-teal) / 0.15);
    --shadow-elevated: 0 8px 30px -8px hsl(var(--brand-teal) / 0.25);

    /* Animation */
    --transition-smooth: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);

    /* Toast Progress Animation */
    @keyframes progress {
      from {
        width: 100%;
      }
      to {
        width: 0%;
      }
    }

    /* Toast Entrance Animation */
    @keyframes toast-slide-in {
      from {
        transform: translateX(100%);
        opacity: 0;
      }
      to {
        transform: translateX(0);
        opacity: 1;
      }
    }

    @keyframes toast-slide-out {
      from {
        transform: translateX(0);
        opacity: 1;
      }
      to {
        transform: translateX(100%);
        opacity: 0;
      }
    }

    /* Toast Icon Pulse Animation */
    @keyframes icon-pulse {
      0%,
      100% {
        transform: scale(1);
      }
      50% {
        transform: scale(1.1);
      }
    }

    /* Toast Glow Effect */
    .toast-glow {
      box-shadow: 0 0 20px rgba(34, 197, 94, 0.3);
    }

    --sidebar-background: 0 0% 98%;

    --sidebar-foreground: 240 5.3% 26.1%;

    --sidebar-primary: 240 5.9% 10%;

    --sidebar-primary-foreground: 0 0% 98%;

    --sidebar-accent: 240 4.8% 95.9%;

    --sidebar-accent-foreground: 240 5.9% 10%;

    --sidebar-border: 220 13% 91%;

    --sidebar-ring: 217.2 91.2% 59.8%;
  }

  .dark {
    --background: 208 30% 8%;
    --foreground: 0 0% 98%;

    --card: 208 25% 12%;
    --card-foreground: 0 0% 98%;

    --popover: 208 25% 12%;
    --popover-foreground: 0 0% 98%;

    --primary: 195 85% 65%;
    --primary-foreground: 208 25% 15%;

    --secondary: 208 15% 18%;
    --secondary-foreground: 0 0% 90%;

    --muted: 208 15% 15%;
    --muted-foreground: 208 10% 60%;

    --accent: 195 35% 25%;
    --accent-foreground: 0 0% 90%;

    --destructive: 0 62.8% 50%;
    --destructive-foreground: 0 0% 98%;

    --border: 208 15% 20%;
    --input: 208 15% 18%;
    --ring: 195 85% 65%;
    --sidebar-background: 240 5.9% 10%;
    --sidebar-foreground: 240 4.8% 95.9%;
    --sidebar-primary: 224.3 76.3% 48%;
    --sidebar-primary-foreground: 0 0% 100%;
    --sidebar-accent: 240 3.7% 15.9%;
    --sidebar-accent-foreground: 240 4.8% 95.9%;
    --sidebar-border: 240 3.7% 15.9%;
    --sidebar-ring: 217.2 91.2% 59.8%;
  }
}

@layer base {
  * {
    @apply border-border;
  }

  body {
    @apply bg-background text-foreground;
    font-family: "Almarai", sans-serif;
    font-feature-settings: "rlig" 1, "calt" 1;
  }

  /* All text now uses Almarai font by default */

  /* Smooth scrolling */
  html {
    scroll-behavior: smooth;
  }

  /* Custom animations */
  @keyframes fadeInUp {
    from {
      opacity: 0;
      transform: translateY(30px);
    }
    to {
      opacity: 1;
      transform: translateY(0);
    }
  }

  @keyframes fadeIn {
    from {
      opacity: 0;
    }
    to {
      opacity: 1;
    }
  }

  @keyframes slideInLeft {
    from {
      opacity: 0;
      transform: translateX(-50px);
    }
    to {
      opacity: 1;
      transform: translateX(0);
    }
  }

  @keyframes slideInRight {
    from {
      opacity: 0;
      transform: translateX(50px);
    }
    to {
      opacity: 1;
      transform: translateX(0);
    }
  }

  @keyframes scaleIn {
    from {
      opacity: 0;
      transform: scale(0.9);
    }
    to {
      opacity: 1;
      transform: scale(1);
    }
  }

  @keyframes float {
    0%,
    100% {
      transform: translateY(0px);
    }
    50% {
      transform: translateY(-20px);
    }
  }

  @keyframes pulse-glow {
    0%,
    100% {
      box-shadow: 0 0 20px rgba(var(--brand-teal-rgb), 0.3);
    }
    50% {
      box-shadow: 0 0 40px rgba(var(--brand-teal-rgb), 0.6);
    }
  }

  .animate-fade-in-up {
    animation: fadeInUp 0.6s ease-out forwards;
  }

  .animate-fade-in {
    animation: fadeIn 0.8s ease-out forwards;
  }

  .animate-slide-in-left {
    animation: slideInLeft 0.6s ease-out forwards;
  }

  .animate-slide-in-right {
    animation: slideInRight 0.6s ease-out forwards;
  }

  .animate-scale-in {
    animation: scaleIn 0.5s ease-out forwards;
  }

  .animate-float {
    animation: float 6s ease-in-out infinite;
  }

  .animate-pulse-glow {
    animation: pulse-glow 2s ease-in-out infinite;
  }

  /* New enhanced animations for features section */
  @keyframes spin-slow {
    from {
      transform: rotate(0deg);
    }
    to {
      transform: rotate(360deg);
    }
  }

  @keyframes shimmer {
    0% {
      background-position: -200% 0;
    }
    100% {
      background-position: 200% 0;
    }
  }

  @keyframes gradient-shift {
    0%,
    100% {
      background-position: 0% 50%;
    }
    50% {
      background-position: 100% 50%;
    }
  }

  .animate-spin-slow {
    animation: spin-slow 3s linear infinite;
  }

  .animate-shimmer {
    background: linear-gradient(
      90deg,
      transparent,
      rgba(255, 255, 255, 0.2),
      transparent
    );
    background-size: 200% 100%;
    animation: shimmer 2s infinite;
  }

  .animate-gradient {
    background-size: 200% 200%;
    animation: gradient-shift 3s ease infinite;
  }

  /* Hover effects */
  .hover-lift {
    transition: transform 0.3s ease, box-shadow 0.3s ease;
  }

  .hover-lift:hover {
    transform: translateY(-4px);
    box-shadow: var(--shadow-elevated);
  }

  /* Enhanced card effects */
  .card-perspective {
    perspective: 1000px;
  }

  .card-3d {
    transform-style: preserve-3d;
    transition: transform 0.6s cubic-bezier(0.23, 1, 0.32, 1);
  }

  .card-3d:hover {
    transform: rotateY(5deg) rotateX(5deg);
  }

  /* RTL Support */
  [dir="rtl"] {
    direction: rtl;
  }

  [dir="rtl"] .text-left {
    text-align: right;
  }

  [dir="rtl"] .text-right {
    text-align: left;
  }

  [dir="rtl"] .justify-start {
    justify-content: flex-end;
  }

  [dir="rtl"] .justify-end {
    justify-content: flex-start;
  }

  /* Almarai font is now the default for all text, including RTL */

  /* Adjust spacing for Arabic text */
  [dir="rtl"] .tracking-tight {
    letter-spacing: 0;
  }

  [dir="rtl"] .tracking-wide {
    letter-spacing: 0.025em;
  }

  /* Modern Calendar Styles */
  .calendar-container {
    min-width: 280px;
    max-width: 100%;
    font-family: inherit;
  }

  .calendar-header {
    user-select: none;
  }

  .nav-button {
    transition: all 0.2s ease-in-out;
    touch-action: manipulation;
  }

  .nav-button:hover {
    background-color: #f3f4f6;
    transform: scale(1.05);
  }

  .nav-button:active {
    transform: scale(0.95);
  }

  .day-button {
    touch-action: manipulation;
    user-select: none;
    position: relative;
    overflow: hidden;
  }

  .day-button::before {
    content: "";
    position: absolute;
    top: 50%;
    left: 50%;
    width: 0;
    height: 0;
    background-color: rgba(59, 130, 246, 0.1);
    border-radius: 50%;
    transform: translate(-50%, -50%);
    transition: width 0.3s ease, height 0.3s ease;
  }

  .day-button:hover::before {
    width: 100%;
    height: 100%;
  }

  .day-button:focus {
    z-index: 10;
  }

  /* Mobile optimizations */
  @media (max-width: 640px) {
    .calendar-container {
      padding: 1rem;
      border-radius: 0.75rem;
    }

    .calendar-header {
      margin-bottom: 1rem;
    }

    .calendar-header h2 {
      font-size: 1rem;
      font-weight: 600;
    }

    .nav-button {
      padding: 0.5rem;
      border-radius: 0.5rem;
    }

    .day-button {
      height: 2.75rem;
      width: 2.75rem;
      font-size: 0.875rem;
    }

    .weekday-header {
      padding: 0.5rem 0;
      font-size: 0.75rem;
    }

    .weeks {
      gap: 0.25rem;
    }

    .week {
      gap: 0.25rem;
    }
  }

  /* Touch device optimizations */
  @media (hover: none) and (pointer: coarse) {
    .day-button {
      height: 3rem;
      width: 3rem;
      font-size: 1rem;
    }

    .nav-button {
      padding: 0.75rem;
      min-height: 44px;
      min-width: 44px;
    }

    .calendar-container {
      padding: 1.25rem;
    }
  }

  /* High contrast mode support */
  @media (prefers-contrast: high) {
    .calendar-container {
      border: 2px solid;
    }

    .day-button {
      border: 1px solid transparent;
    }

    .day-button:focus {
      border-color: currentColor;
    }
  }

  /* Reduced motion support */
  @media (prefers-reduced-motion: reduce) {
    .day-button,
    .nav-button {
      transition: none;
    }

    .day-button::before {
      transition: none;
    }

    .nav-button:hover {
      transform: none;
    }

    .nav-button:active {
      transform: none;
    }
  }

  /* Dark mode support */
  @media (prefers-color-scheme: dark) {
    .calendar-container {
      background-color: #1f2937;
      border-color: #374151;
      color: #f9fafb;
    }

    .calendar-header h2 {
      color: #f9fafb;
    }

    .weekday-header {
      color: #9ca3af;
    }

    .nav-button:hover {
      background-color: #374151;
    }

    .day-button {
      color: #f9fafb;
    }

    .day-button:hover {
      background-color: #374151;
      color: #60a5fa;
    }

    .day-button[aria-selected="true"] {
      background-color: #2563eb;
      color: #ffffff;
    }

    .day-button[aria-disabled="true"] {
      color: #6b7280;
    }
  }
}
