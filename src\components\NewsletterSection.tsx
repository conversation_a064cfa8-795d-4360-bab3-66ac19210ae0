import { useState } from "react";
import { useTranslation } from "react-i18next";
import { useLanguage } from "@/contexts/LanguageContext";
import { But<PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Card, CardContent } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Mail, Send, Gift, Star, Users, Zap } from "lucide-react";

const NewsletterSection = () => {
  const { t } = useTranslation();
  const { isRTL } = useLanguage();
  const [email, setEmail] = useState("");
  const [isSubscribed, setIsSubscribed] = useState(false);

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    if (email) {
      setIsSubscribed(true);
      setEmail("");
      // Here you would typically handle the subscription
    }
  };

  const benefits = [
    {
      icon: Gift,
      title: t("newsletter.benefits.exclusiveDeals.title"),
      description: t("newsletter.benefits.exclusiveDeals.description"),
    },
    {
      icon: Star,
      title: t("newsletter.benefits.premiumContent.title"),
      description: t("newsletter.benefits.premiumContent.description"),
    },
    {
      icon: Zap,
      title: t("newsletter.benefits.firstToKnow.title"),
      description: t("newsletter.benefits.firstToKnow.description"),
    },
  ];

  if (isSubscribed) {
    return (
      <section className="py-24 bg-gradient-to-br from-primary/5 via-background to-brand-warm/20">
        <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 text-center">
          <div className="bg-gradient-to-r from-green-50 to-emerald-50 rounded-3xl p-12 border border-green-200">
            <div className="w-20 h-20 bg-gradient-to-r from-green-500 to-emerald-500 rounded-full flex items-center justify-center mx-auto mb-6">
              <Mail className="w-10 h-10 text-white" />
            </div>
            <h2 className="text-3xl font-bold text-foreground mb-4">
              {t("newsletter.successTitle")}
            </h2>
            <p className="text-lg text-muted-foreground mb-6">
              {t("newsletter.successDescription")}
            </p>
            <div
              className={`flex items-center justify-center text-sm text-muted-foreground ${
                isRTL ? "space-x-reverse space-x-4" : "space-x-4"
              }`}
            >
              <div
                className={`flex items-center ${
                  isRTL ? "space-x-reverse space-x-1" : "space-x-1"
                }`}
              >
                <Users className="w-4 h-4" />
                <span>{t("newsletter.joinSubscribers")}</span>
              </div>
              <div
                className={`flex items-center ${
                  isRTL ? "space-x-reverse space-x-1" : "space-x-1"
                }`}
              >
                <Star className="w-4 h-4 fill-yellow-400 text-yellow-400" />
                <span>{t("newsletter.successRating")}</span>
              </div>
            </div>
          </div>
        </div>
      </section>
    );
  }

  return (
    <section className="py-24 bg-gradient-to-br from-primary/5 via-background to-brand-warm/20 relative overflow-hidden">
      {/* Background decorations */}
      <div className="absolute top-10 left-10 opacity-10">
        <Mail className="w-24 h-24 text-primary" />
      </div>
      <div className="absolute bottom-10 right-10 opacity-10">
        <Send className="w-20 h-20 text-primary" />
      </div>

      <div className="max-w-6xl mx-auto px-4 sm:px-6 lg:px-8">
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-12 items-center">
          {/* Left side - Benefits */}
          <div>
            <div
              className={`inline-flex items-center bg-gradient-to-r from-primary/15 via-primary/10 to-brand-ocean/15 border border-primary/20 rounded-full px-6 py-3 mb-6 shadow-soft hover:shadow-elevated transition-all duration-500 hover:scale-105 group backdrop-blur-sm relative overflow-hidden`}
            >
              {/* Animated background glow */}
              <div className="absolute inset-0 bg-gradient-to-r from-primary/5 to-brand-ocean/5 rounded-full opacity-0 group-hover:opacity-100 transition-opacity duration-500"></div>

              {/* Shimmer effect */}
              <div className="absolute inset-0 bg-gradient-to-r from-transparent via-white/10 to-transparent -translate-x-full group-hover:translate-x-full transition-transform duration-1000 rounded-full"></div>

              <div
                className={`relative z-10 w-5 h-5 rounded-full bg-gradient-to-r from-primary to-brand-ocean flex items-center justify-center ${
                  isRTL ? "ml-3" : "mr-3"
                } group-hover:rotate-12 transition-transform duration-300`}
              >
                <Mail className="w-3 h-3 text-white" />
              </div>

              <span className="relative z-10 text-primary text-sm font-bold tracking-wide group-hover:text-foreground transition-colors duration-300">
                {t("newsletter.stayConnected")}
              </span>

              {/* Pulse dot indicator */}
              <div
                className={`relative z-10 w-2 h-2 bg-primary rounded-full ${
                  isRTL ? "mr-2" : "ml-2"
                } animate-pulse`}
              >
                <div className="absolute inset-0 bg-primary rounded-full animate-ping opacity-75"></div>
              </div>
            </div>

            <p className="text-lg text-muted-foreground mb-8 leading-relaxed">
              {t("newsletter.description")}
            </p>

            <div className="space-y-4">
              {benefits.map((benefit, index) => (
                <div
                  key={index}
                  className={`flex items-start p-4 rounded-xl bg-background/50 hover:bg-background/80 transition-colors duration-300 ${
                    isRTL ? "space-x-reverse space-x-4" : "space-x-4"
                  }`}
                  style={{ animationDelay: `${index * 100}ms` }}
                >
                  <div className="w-10 h-10 bg-gradient-to-r from-primary to-brand-ocean rounded-lg flex items-center justify-center flex-shrink-0">
                    <benefit.icon className="w-5 h-5 text-white" />
                  </div>
                  <div>
                    <h3 className="font-semibold text-foreground mb-1">
                      {benefit.title}
                    </h3>
                    <p className="text-sm text-muted-foreground">
                      {benefit.description}
                    </p>
                  </div>
                </div>
              ))}
            </div>

            {/* Trust indicators */}
            <div
              className={`flex items-center mt-8 text-sm text-muted-foreground ${
                isRTL ? "space-x-reverse space-x-6" : "space-x-6"
              }`}
            >
              <div
                className={`flex items-center ${
                  isRTL ? "space-x-reverse space-x-1" : "space-x-1"
                }`}
              >
                <Users className="w-4 h-4" />
                <span>{t("newsletter.subscriberCount")}</span>
              </div>
              <div
                className={`flex items-center ${
                  isRTL ? "space-x-reverse space-x-1" : "space-x-1"
                }`}
              >
                <Star className="w-4 h-4 fill-yellow-400 text-yellow-400" />
                <span>{t("newsletter.rating")}</span>
              </div>
              <Badge
                variant="secondary"
                className="bg-green-100 text-green-700"
              >
                {t("newsletter.noSpam")}
              </Badge>
            </div>
          </div>

          {/* Right side - Newsletter form */}
          <div>
            <Card className="border-0 shadow-elevated bg-gradient-to-br from-background to-brand-warm/30 overflow-hidden">
              <CardContent className="p-8">
                <div className="text-center mb-8">
                  <div className="w-16 h-16 bg-gradient-to-r from-primary to-brand-ocean rounded-2xl flex items-center justify-center mx-auto mb-4">
                    <Mail className="w-8 h-8 text-white" />
                  </div>
                  <h3 className="text-2xl font-bold text-foreground mb-2">
                    {t("newsletter.formTitle")}
                  </h3>
                  <p className="text-muted-foreground">
                    {t("newsletter.formDescription")}
                  </p>
                </div>

                <form onSubmit={handleSubmit} className="space-y-4">
                  <div className="relative">
                    <Input
                      type="email"
                      placeholder={t("newsletter.emailPlaceholder")}
                      value={email}
                      onChange={(e) => setEmail(e.target.value)}
                      className={`h-12 border-border focus:border-primary transition-colors ${
                        isRTL ? "pl-12" : "pr-12"
                      }`}
                      required
                    />
                    <Mail
                      className={`absolute top-1/2 transform -translate-y-1/2 w-5 h-5 text-muted-foreground ${
                        isRTL ? "left-4" : "right-4"
                      }`}
                    />
                  </div>

                  <Button
                    type="submit"
                    className="w-full h-12 bg-gradient-ocean hover:opacity-90 text-white shadow-soft hover:shadow-elevated transition-all duration-300 hover:scale-105 font-semibold"
                  >
                    <Send className={`w-5 h-5 ${isRTL ? "ml-2" : "mr-2"}`} />
                    {t("newsletter.subscribeButton")}
                  </Button>
                </form>

                <div className="text-center mt-6">
                  <p className="text-xs text-muted-foreground">
                    {t("newsletter.privacyText")}
                    <br />
                    {t("newsletter.unsubscribeText")}
                  </p>
                </div>

                {/* Decorative elements */}
                <div className="absolute -top-4 -right-4 w-24 h-24 bg-gradient-to-br from-primary/20 to-transparent rounded-full"></div>
                <div className="absolute -bottom-4 -left-4 w-16 h-16 bg-gradient-to-tr from-brand-ocean/20 to-transparent rounded-full"></div>
              </CardContent>
            </Card>
          </div>
        </div>
      </div>
    </section>
  );
};

export default NewsletterSection;
